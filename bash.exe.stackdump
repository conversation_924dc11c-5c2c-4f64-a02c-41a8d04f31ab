Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FE8E
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210286019, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB730  000210068E24 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA10  00021006A225 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9CBDB0000 ntdll.dll
7FF9C9A40000 KERNEL32.DLL
7FF9C9130000 KERNELBASE.dll
7FF9CA8F0000 USER32.dll
7FF9C9680000 win32u.dll
7FF9CBD40000 GDI32.dll
7FF9C97F0000 gdi32full.dll
7FF9C99A0000 msvcp_win.dll
7FF9C8FE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9CA530000 advapi32.dll
7FF9CB770000 msvcrt.dll
7FF9CA840000 sechost.dll
7FF9C9100000 bcrypt.dll
7FF9CA390000 RPCRT4.dll
7FF9C8640000 CRYPTBASE.DLL
7FF9C96B0000 bcryptPrimitives.dll
7FF9CBAC0000 IMM32.DLL
