#!/usr/bin/env python3
"""
FastAPI main application file for the Vanna PostgreSQL Ollama ChromaDB application.
Converted from Flask to FastAPI.
"""

import logging
import sys
import os
import time
import traceback
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from config import Config
from app.utils import MemoryCache
from app.core import setup_vanna
from app.api.main_routes import router as main_router
from app.api.sql_routes import router as sql_router
from app.api.analysis_routes import router as analysis_router
from app.api.dashboard_routes import router as dashboard_router
from app.api.search_routes import router as search_router
from app.auth.routes import router as auth_router

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# Global variables
vn = None
cache = None
config = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events"""
    global vn, cache, config
    
    logger.info("=== APPLICATION STARTUP INITIATED ===")
    logger.info(f"Startup time: {datetime.now().isoformat()}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    
    try:
        # Load configuration
        logger.info("Loading application configuration...")
        config = Config()
        logger.info(f"Configuration loaded successfully: {config.__class__.__name__}")
        
        # Initialize cache
        logger.info("Initializing cache...")
        cache = MemoryCache(
            max_size=config.CACHE_MAX_SIZE, 
            expiration_seconds=config.CACHE_EXPIRATION_SECONDS
        )
        logger.info("Cache initialized successfully!")
        
        # Initialize Vanna
        logger.info("Initializing Vanna...")
        vn = setup_vanna()
        logger.info("Vanna initialized successfully!")
        
        # Store instances in app state
        app.state.vn = vn
        app.state.cache = cache
        app.state.config = config
        
        # Log startup information
        logger.info("=== APPLICATION CONFIGURATION ===")
        print("🚀 Starting Vanna FastAPI Application...")
        print(f"🌐 Access the application at: http://localhost:8000")
        print(f"🔧 Debug mode: {config.DEBUG}")
        print(f"📊 Database: {config.DB_HOST}:{config.DB_PORT}/{config.DB_NAME}")
        print(f"🤖 Ollama model: {config.OLLAMA_MODEL}")
        print("=" * 60)
        print("🔍 MODEL INPUT/OUTPUT LOGGING ENABLED")
        print("📋 You will see detailed logs in the terminal showing:")
        print("   • Full prompts sent to the model")
        print("   • Context data (DDL, docs, examples)")
        print("   • Real-time model responses")
        print("   • Summary and chart generation requests")
        print("   • SQL correction attempts")
        print("=" * 60)
        
        logger.info("=== FASTAPI SERVER READY ===")
        
    except Exception as e:
        logger.error(f"Application startup failed: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.exception("Full traceback:")
        raise
    
    yield  # Application is running
    
    # Shutdown
    logger.info("=== APPLICATION SHUTDOWN ===")
    logger.info(f"Shutdown time: {datetime.now().isoformat()}")

# Create FastAPI app
app = FastAPI(
    title="Hospital Assistant System",
    description="AI-powered hospital management system with natural language SQL queries",
    version="1.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])

# Request/Response middleware for performance monitoring
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    logger.info(f"Request to {request.url.path} took {process_time:.2f}s")
    return response

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}\n{traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={"type": "error", "error": "An unexpected error occurred. Please try again later."}
    )

# Include API routers BEFORE mounting static files
app.include_router(main_router)
app.include_router(auth_router, prefix="/auth", tags=["authentication"])
app.include_router(sql_router, prefix="/api/v0", tags=["sql"])
app.include_router(analysis_router, prefix="/api/v0", tags=["analysis"])
app.include_router(dashboard_router, prefix="/api/v0", tags=["dashboard"])
app.include_router(search_router, prefix="/api/v0", tags=["search"])

# Static files mounting disabled - using React frontend instead
# app.mount("/", StaticFiles(directory="static", html=True), name="static")

# Utility functions to get global instances
def get_vanna():
    """Get the global Vanna instance"""
    return vn

def get_cache():
    """Get the global cache instance"""
    return cache

def get_config():
    """Get the global config instance"""
    return config

if __name__ == "__main__":
    import uvicorn
    
    # Load config for uvicorn settings
    config = Config()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=config.DEBUG,
        log_level="info"
    )
