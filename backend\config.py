import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key_change_in_production')
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    USE_RELOADER = os.environ.get('USE_RELOADER', 'False').lower() == 'true'
    
    # Database configuration
    DB_HOST = os.environ.get('DB_HOST', 'aws-0-ap-southeast-1.pooler.supabase.com')
    DB_NAME = os.environ.get('DB_NAME', 'postgres')
    DB_USER = os.environ.get('DB_USER', 'postgres.ixajpflnkexezmsxyofd')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '123456')
    if not DB_PASSWORD:
        raise ValueError("DB_PASSWORD environment variable must be set")
    try:
        DB_PORT = int(os.environ.get('DB_PORT', 5432))
        if not (1 <= DB_PORT <= 65535):
            raise ValueError(f"Invalid port number: {DB_PORT}")
    except ValueError as e:
        raise ValueError(f"Invalid DB_PORT value: {e}")
    
    # Ollama configuration
    OLLAMA_MODEL = os.environ.get('OLLAMA_MODEL', 'qwen3:4b')
    OLLAMA_HOST = os.environ.get('OLLAMA_HOST', 'localhost')
    
    # Cache configuration
    CACHE_MAX_SIZE = 150
    CACHE_EXPIRATION_SECONDS = 7200  # 2 hours
    
    # SQL execution limits
    MAX_SQL_ROWS = 1000
    MAX_DF_SIZE_MB = 50
    SAMPLE_DF_SIZE_MB = 10
    
    @property
    def database_config(self):
        return {
            'host': self.DB_HOST,
            'dbname': self.DB_NAME,
            'user': self.DB_USER,
            'password': self.DB_PASSWORD,
            'port': self.DB_PORT
        }
    
    @property
    def vanna_config(self):
        return {
            'model': self.OLLAMA_MODEL,
            'dialect': 'PostgreSQL',
            'language': 'English',
            'host': self.OLLAMA_HOST,
            'max_tokens': 14000,
            'path': './chromadb'  # Custom path for ChromaDB storage
        }
