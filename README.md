# Vanna PostgreSQL Ollama ChromaDB Application

This project is a Flask-based application that integrates Vanna.ai with a PostgreSQL database, Ollama for LLM capabilities, and ChromaDB for vector storage.

## Features

*   **Vanna.ai Integration:** Leverages Vanna for natural language to SQL generation.
*   **PostgreSQL Database:** Connects to a PostgreSQL database for data storage and querying.
*   **Ollama LLM:** Uses a local Ollama instance for large language model processing.
*   **Flask Web Framework:** Provides a web interface for interacting with the application.
*   **Configuration Management:** Uses environment variables for flexible configuration.
*   **Comprehensive Logging:** Detailed logging for debugging and monitoring.

## Project Structure

```
.
├── app/
│   ├── __init__.py
│   ├── api/
│   ├── auth/
│   ├── core/
│   ├── models/
│   └── utils/
├── config.py
├── run.py
├── .env (Example, create your own)
└── README.md
```

## How it Works

This application enables users to query a PostgreSQL database using natural language. It leverages Vanna.ai, powered by an Ollama LLM and ChromaDB for vector storage, all orchestrated by a Flask backend.

**1. Initialization & Setup:**
    - The application starts via `run.py`, which initializes the Flask app using `create_app` from `app/__init__.py`.
    - A crucial step in `create_app` (defined in `app/__init__.py`) involves initializing services, including setting up Vanna by calling `setup_vanna` (from `app.core.database`).
    - `setup_vanna` (`app/core/database.py`):
        - Initializes `MyVanna` (from `app.core.vanna_custom.py`), which is a custom class combining Vanna's Ollama LLM interface and ChromaDB vector store capabilities.
        - Connects `MyVanna` to the PostgreSQL database using credentials sourced from `config.py` (which reads environment variables).
        - Performs initial training if no existing training data is found in ChromaDB. This involves:
            - Extracting schema information (table and column names relevant to the application).
            - Training on predefined DDL (Data Definition Language) statements.
            - Training on provided documentation (text describing table relationships, column meanings, and business context).
            - This training data (schema, DDL, docs) is converted into vector embeddings and stored in ChromaDB for efficient similarity searches during query processing.

**2. User Interface & Authentication:**
    - The main web interface (typically `index.html` and other static assets) is served by `app.api.main_routes.py` (specifically the `/` route).
    - User authentication (login/logout functionalities) is handled by routes defined in `app.auth.routes.py` (e.g., `/login`, `/logout`).

**3. Core Query Workflow (Natural Language to SQL to Results):**
    - **Asking a Question:**
        - The user types a question in natural language into the web UI.
        - The UI sends this question to the `/api/sql/generate_sql` endpoint, managed by `app.api.sql_routes.py`.
    - **SQL Generation (`MyVanna.generate_interactive_response` in `app.core.vanna_custom.py`):**
        - This is the core Vanna logic for converting natural language to SQL.
        - It retrieves relevant context from ChromaDB by performing similarity searches against the user's question. This context includes:
            - Similar question-SQL pairs previously trained.
            - DDL statements of tables deemed relevant to the question.
            - Documentation snippets related to the question.
        - It also considers the current chat history for multi-turn conversations, allowing follow-up questions.
        - A detailed prompt is constructed for the Ollama LLM, including the user's question, the retrieved context, and specific instructions for SQL generation and thought processes (e.g., using `<think>` blocks).
        - The LLM's response is streamed back to the UI via `text/event-stream`. This stream can include:
            - "Thinking" indicators (processed from `<think>` blocks by `MyVanna` and signaled to the UI).
            - Conversational chat chunks (if the LLM provides explanations or asks for clarification).
            - The final generated SQL query.
        - The user's question and the generated SQL are cached (using a cache mechanism likely involving `app.utils.decorators.requires_cache` and `app.cache` instance).
    - **Executing SQL:**
        - The user can choose to run the generated SQL (or a corrected version).
        - The UI sends the SQL to the `/api/sql/run_sql` endpoint in `app.api.sql_routes.py`.
        - `MyVanna.run_sql` (a method likely inherited or defined in `vanna.base` and used by `MyVanna`) executes the query against the connected PostgreSQL database.
            - A `LIMIT` (from `config.MAX_SQL_ROWS`) is automatically added to SELECT queries without one to prevent excessively large result sets.
        - **Error Handling & Correction:** If the initial SQL execution fails:
            - `MyVanna.generate_corrected_sql` is invoked. This method sends the original question, the failed SQL, and the database error message to the LLM to attempt a correction.
            - If a valid corrected SQL is generated and is different from the original, it's executed.
        - The resulting pandas DataFrame is cached (a sample is cached if the DataFrame exceeds `config.MAX_DF_SIZE_MB` or `config.SAMPLE_DF_SIZE_MB`).
        - A sample of the DataFrame (e.g., first 10 rows) and metadata (total rows, column names, whether it was sampled) are returned to the UI.

**4. Data Analysis and Visualization (via `app.api.analysis_routes.py`):**
    - **Summaries:**
        - Users can request a textual summary of the query results (endpoints: `/api/analysis/generate_summary` or `/api/analysis/generate_summary_stream`).
        - `MyVanna.generate_summary` or `MyVanna.generate_summary_stream` sends the user's question and a sample of the DataFrame to the LLM to generate a concise summary. The streaming endpoint provides real-time updates.
    - **Charts:**
        - Users can request a chart visualization of the data (endpoint: `/api/analysis/generate_plotly_figure`).
        - `MyVanna.should_generate_chart_with_context` uses the LLM to:
            - Analyze the question, SQL, and DataFrame structure to determine if a chart is appropriate and beneficial.
            - If yes, it instructs the LLM to generate Python code for a Plotly figure.
        - `MyVanna.get_plotly_figure` (a Vanna base method) then attempts to create an interactive Plotly figure using the LLM-generated code and the DataFrame.
        - The Plotly figure (as JSON) is returned to the UI for rendering.
    - **CSV Download:**
        - Users can download the query result DataFrame as a CSV file (endpoint: `/api/analysis/download_csv`). Data might be truncated for very large results.

**5. Training Management (via `app.api.training_routes.py`):**
    - The application allows for ongoing improvement of Vanna's knowledge and accuracy:
        - **Adding Training Data (`/api/train/train`):** Users (likely administrators or through a specific interface) can add new question-SQL pairs, DDL statements, or documentation snippets. `MyVanna.train` processes this data and stores its vector embeddings in ChromaDB.
        - **Viewing Training Data (`/api/train/get_training_data`):** Retrieves existing training data from Vanna.
        - **Removing Training Data (`/api/train/remove_training_data`):** Deletes specific training entries by their ID from ChromaDB.
        - **Generating Sample Questions (`/api/train/generate_questions`):** `MyVanna.generate_questions` asks the LLM to suggest new, relevant questions based on the existing training data (schema, DDLs, docs). This helps discover knowledge gaps and areas for further training.
    - **Question History & Cache Loading:**
        - `/api/train/get_question_history`: Retrieves a list of previously asked questions from the application's cache.
        - `/api/train/load_question`: Allows reloading a complete cached interaction (question, SQL, DataFrame, and potentially a chart figure) by its ID.

**Key Components & Technologies:**
    - **Flask:** A Python web framework used for the backend API and potentially serving the static frontend files.
    - **`MyVanna` (`app.core.vanna_custom.py`):** This is the central custom class that orchestrates most of the AI and database interaction logic. It extends Vanna's base classes and customizes behavior. It integrates:
        - **Ollama (`vanna.ollama.Ollama`):** An interface to a locally running Large Language Model (e.g., `qwen3:4b` as per `config.py`). The LLM is responsible for all AI-driven tasks: natural language understanding, SQL generation, data summarization, charting decisions, and SQL correction.
        - **ChromaDB (`vanna.chromadb.ChromaDB_VectorStore`):** A vector database used to store and retrieve training data (DDL, documentation, question-SQL pairs) efficiently using vector similarity searches. This provides the necessary context for the LLM.
    - **PostgreSQL:** The target relational database that holds the business data being queried.
    - **Pandas:** A Python library used extensively for data manipulation, especially for handling the results of SQL queries as DataFrames.
    - **Plotly:** A Python library for generating interactive charts and visualizations.
    - **Caching:** A server-side caching mechanism (details evident from `app.utils.decorators.requires_cache`, `app.cache`, and various `cache.get/set` calls) is used to store frequently accessed data like questions, generated SQL queries, DataFrames, Plotly figures, and chat history. This improves performance and manages state across requests.
    - **Configuration (`config.py`):** Manages all application settings, including database connection details, Ollama model URI and parameters, cache settings, and SQL execution limits. It primarily sources these settings from environment variables (loaded via `python-dotenv`).
    - **Flask Blueprints (`app.api.*`, `app.auth.*`):** Used to organize Flask routes into modular components, making the application structure cleaner (e.g., `sql_bp`, `analysis_bp`, `training_bp`, `auth_bp`).

This detailed workflow allows the application to provide a responsive, interactive, and intelligent natural language interface to the PostgreSQL database, with capabilities for continuous learning and improvement through the Vanna training mechanisms.

## Configuration

The application uses environment variables for configuration. Create a `.env` file in the root directory of the project with the following variables:

```env
# Flask Configuration
SECRET_KEY=your_secret_key_here
DEBUG=True
USE_RELOADER=True

# Database Configuration
DB_HOST=localhost
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your_db_password
DB_PORT=5432

# Ollama Configuration
OLLAMA_MODEL=qwen3:4b
OLLAMA_HOST=localhost

# Cache Configuration (Optional - defaults are provided in config.py)
# CACHE_MAX_SIZE=150
# CACHE_EXPIRATION_SECONDS=7200

# SQL Execution Limits (Optional - defaults are provided in config.py)
# MAX_SQL_ROWS=1000
# MAX_DF_SIZE_MB=50
# SAMPLE_DF_SIZE_MB=10
```

**Note:**
*   `DB_PASSWORD` is a mandatory environment variable.
*   Ensure the `DB_PORT` is a valid port number (1-65535).

## How to Run

1.  **Set up Environment Variables:**
    Create a `.env` file as described in the Configuration section.

2.  **Install Dependencies:**
    (Assuming you have a `requirements.txt` file)
    ```bash
    pip install -r requirements.txt
    ```

3.  **Run the Application:**
    ```bash
    python run.py
    ```

    The application will be accessible at `http://localhost:5000`.

## Logging

The application generates logs in `app.log` and also outputs logs to the console. Logs include detailed information about application startup, configuration, model inputs/outputs, and any errors.

- Users can access dashboard statistics through the `/api/dashboard/overview`, `/api/dashboard/patients`, `/api/dashboard/consultations`, `/api/dashboard/diagnoses`, and `/api/dashboard/biometrics` endpoints. 