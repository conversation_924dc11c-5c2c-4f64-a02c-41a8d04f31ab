<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hospital Assistant System</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        :root {
            --bg-primary: #f8f9fa; /* Lighter primary background */
            --bg-secondary: #ffffff;
            --bg-nav: #eef2f5; /* Softer nav background */
            --text-primary: #212529; /* Darker primary text */
            --text-secondary: #495057;
            --text-muted: #6c757d;
            --accent-color: #007bff; /* Standard Bootstrap blue */
            --accent-hover: #0056b3;
            --border-color: #dee2e6; /* Softer border */
            --soft-border: #e9ecef;
            --user-bubble: #007bff;
            --user-text: #ffffff;
            --bot-bubble: #e9ecef; /* Softer bot bubble */
            --bot-text: #212529;
            --header-shadow: rgba(0,0,0,0.03); /* Softer shadow */
            --button-shadow: rgba(0, 0, 0, 0.05);
        }

        /* Dark Theme */
        [data-theme="dark"] {
            --bg-primary: #121212; /* Deeper dark */
            --bg-secondary: #1e1e1e; /* Slightly lighter secondary */
            --bg-nav: #242424; /* Darker nav */
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --accent-color: #0d6efd; /* Brighter blue for dark mode */
            --accent-hover: #3b82f6;
            --border-color: #343a40; /* Darker border */
            --soft-border: #2c2f33;
            --user-bubble: #0d6efd;
            --user-text: #f0f0f0;
            --bot-bubble: #343a40; /* Darker bot bubble */
            --bot-text: #e0e0e0;
            --header-shadow: rgba(0,0,0,0.1); /* Slightly more pronounced shadow in dark */
            --button-shadow: rgba(255, 255, 255, 0.05);
        }

        body {
            font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .header {
            background-color: var(--bg-secondary);
            padding: 15px 25px; /* Slightly more padding */
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            box-shadow: 0 1px 3px var(--header-shadow); /* Softer shadow */
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .header-title {
            font-size: 1.25em; /* Adjusted size */
            font-weight: 600;
            color: var(--text-primary);
        }
        .header-options a, .header-options span {
            margin-left: 20px; /* Increased spacing */
            text-decoration: none;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.95em;
            transition: color 0.2s ease;
        }
        .header-options a:hover {
            color: var(--accent-color);
            text-decoration: none;
        }
        .theme-toggle {
            display: inline-flex;
            align-items: center;
            margin-left: 18px;
            cursor: pointer;
            color: var(--text-secondary);
        }
        .theme-toggle i {
            margin-right: 4px;
            font-size: 18px;
        }
        .main-container {
            display: flex;
            flex-grow: 1;
            overflow: hidden;
        }
        .nav-menu {
            background-color: var(--bg-nav);
            width: 230px; /* Slightly wider */
            padding: 20px;
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 1px 0 5px rgba(0,0,0,0.03);
        }
        .nav-menu h3 {
            margin-top: 0;
            font-size: 1.05em; /* Adjusted size */
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--soft-border);
            padding-bottom: 15px; /* Increased padding */
            margin-bottom: 18px; /* Increased margin */
        }
        .nav-menu button {
            background-color: transparent;
            border: none;
            padding: 13px 18px; /* Adjusted padding */
            margin-bottom: 8px;
            text-align: left;
            cursor: pointer;
            width: 100%;
            border-radius: 6px; /* Slightly more rounded */
            color: var(--text-secondary);
            font-size: 0.98em; /* Adjusted font size */
            transition: background-color 0.2s ease, color 0.2s ease, transform 0.1s ease;
        }
        .nav-menu button:hover:not(.active) {
            background-color: rgba(0, 0, 0, 0.05); /* Softer hover */
            color: var(--text-primary);
            transform: translateX(2px);
        }
        [data-theme="dark"] .nav-menu button:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.08); /* Lighter hover for dark mode */
            color: var(--text-primary); /* Ensure text remains primary */
        }
        .nav-menu button.active {
            background-color: var(--accent-color);
            color: #ffffff;
            font-weight: 600;
            box-shadow: 0 2px 4px var(--button-shadow);
        }
        .nav-menu button i { /* Ensure icons have some margin */
            margin-right: 10px;
        }
        .chat-interface {
            flex-grow: 1;
            padding: 30px; /* Increased padding */
            display: flex;
            flex-direction: column;
            background-color: var(--bg-secondary);
            overflow: hidden;
            transition: background-color 0.3s ease;
        }
        .chat-interface h2 {
            margin-top: 0;
            font-size: 1.35em; /* Adjusted size */
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--soft-border);
            padding-bottom: 18px; /* Increased padding */
            margin-bottom: 25px; /* Increased margin */
        }
        .chat-history {
            flex-grow: 1;
            border: none;
            padding: 15px; /* Increased padding */
            overflow-y: auto;
            background-color: var(--bg-primary); /* Match primary background for seamless feel */
            margin-bottom: 25px; /* Increased margin */
            min-height: 200px;
            transition: background-color 0.3s ease;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }
        
        /* Scrollbar styling */
        .chat-history::-webkit-scrollbar {
            width: 8px;
        }
        
        .chat-history::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .chat-history::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 4px;
        }
        
        .chat-history::-webkit-scrollbar-thumb:hover {
            background-color: var(--accent-color);
        }
        
        /* Chat message standard */
        .chat-message {
            margin-bottom: 18px; /* Increased margin */
            padding: 12px 18px; /* Adjusted padding */
            border-radius: 20px; /* More rounded bubbles */
            line-height: 1.5;
            max-width: 75%;
            word-wrap: break-word;
            /* Animation properties */
            opacity: 0;
            transform: translateY(15px);
            transition: opacity 0.4s ease-out, transform 0.4s ease-out;
        }
        .chat-message.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .chat-message.user {
            background-color: var(--user-bubble);
            color: var(--user-text);
            margin-left: auto;
            border-bottom-right-radius: 6px; /* Asymmetrical rounding */
            box-shadow: 0 1px 2px rgba(0,0,0,0.08);
        }
        .chat-message.bot {
            background-color: var(--bot-bubble);
            color: var(--bot-text);
            margin-right: auto;
            border-bottom-left-radius: 6px; /* Asymmetrical rounding */
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .chat-message strong {
             font-weight: 600;
             margin-right: 4px; /* Add space after "You:" or "Assistant:" */
        }

        /* Toggle button for collapsible content */
        .sql-toggle-button {
            background-color: var(--accent-hover);
            color: white;
            border: none;
            padding: 6px 12px; /* Adjusted padding */
            margin-top: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.85em; /* Adjusted font size */
            display: inline-block; /* Changed to inline-block */
            transition: background-color 0.2s ease;
            box-shadow: 0 1px 2px var(--button-shadow);
        }
        .sql-toggle-button:hover {
            background-color: var(--accent-color);
            box-shadow: 0 2px 4px var(--button-shadow);
        }
        
        /* Style for multiple toggle buttons in sequence */
        .sql-toggle-button + .sql-toggle-button {
            margin-top: 5px;
        }

        /* Container for collapsible code blocks with smooth animation */
        .sql-code-container {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            background-color: rgba(0,0,0,0.05); /* Subtle background */
            border-radius: 6px;
            margin-top: 0;
            transition: max-height 0.4s ease-in-out, opacity 0.4s ease-in-out, margin-top 0.3s ease-in-out, padding 0.3s ease-in-out;
        }

        /* Dark mode styling for code containers */
        [data-theme="dark"] .sql-code-container {
            background-color: rgba(255,255,255,0.05);
        }

        .sql-code-container.expanded {
            max-height: 500px; /* Adjust as needed */
            opacity: 1;
            margin-top: 8px;
            padding: 10px;
            border: 1px solid var(--soft-border);
        }

        .sql-code-container pre {
            white-space: pre-wrap;    /* CSS3 */
            white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
            white-space: -pre-wrap;   /* Opera 4-6 */
            white-space: -o-pre-wrap;  /* Opera 7 */
            word-wrap: break-word;    /* Internet Explorer 5.5+ */
            margin: 0;
            background-color: transparent; 
            color: var(--text-secondary); /* Use secondary text color for code */
            font-family: 'SF Mono', 'Consolas', 'Monaco', 'Courier New', monospace; /* Modern mono stack */
            font-size: 0.88em; /* Adjusted font size */
            line-height: 1.5; /* Increased line height for readability */
        }
        
        .sql-code-container code {
            display: block; /* Ensure it takes full width of pre */
        }
        
        /* Styling for direct SQL display */
        .sql-display {
            background-color: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px; /* Consistent rounding */
            padding: 12px 15px; /* Adjusted padding */
            margin-top: 8px;
            font-family: 'SF Mono', 'Consolas', 'Monaco', 'Courier New', monospace; /* Modern mono stack */
            font-size: 0.88em; /* Adjusted font size */
            overflow-x: auto;
            white-space: pre-wrap;
            line-height: 1.5; /* Increased line height */
            color: var(--accent-color); /* Keep accent for SQL itself */
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.04);
        }
        
        /* Animation for fading out streaming content */
        .fading-out {
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }
        
        /* Ensure smooth transitions for all content changes */
        .chat-message span {
            transition: opacity 0.3s ease-out;
        }

        /* Styling for LLM processing thoughts state */
        .llm-processing-thoughts {
            font-style: italic;
            color: var(--text-muted); /* Dimmed text */
            opacity: 0.8;
        }

        [data-theme="dark"] .llm-processing-thoughts {
            color: var(--text-secondary); /* Was --text-muted, changed for better contrast */
            opacity: 0.9; /* Slightly increased opacity */
        }

        /* Styling for execution status message */
        .execution-status {
            font-style: italic;
            font-size: 0.9em;
            color: var(--text-muted);
            margin-top: 8px;
            padding: 5px 0;
            animation: textShimmer 1.8s linear infinite;
        }
        
        /* Styling for tables inside chat messages */
        .chat-message table {
            width: auto;
            max-width: 100%;
            border-collapse: collapse; /* Changed for cleaner lines */
            border-spacing: 0;
            margin-top: 12px; /* Adjusted margin */
            font-size: 0.88em; /* Adjusted font size */
            background-color: var(--bg-secondary); /* Use secondary background */
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .chat-message th, .chat-message td {
            border: none; /* Remove internal borders initially */
            padding: 10px 12px; /* Adjusted padding */
            text-align: left;
            white-space: normal;
            border-bottom: 1px solid var(--soft-border); /* Border only between rows */
        }
        .chat-message td:not(:last-child), 
        .chat-message th:not(:last-child) {
             border-right: 1px solid var(--soft-border); /* Vertical borders */
        }
        .chat-message tr:last-child td {
            border-bottom: none;
        }
        .chat-message th {
            background-color: var(--bg-nav); /* Use nav background for header */
            font-weight: 600;
            color: var(--text-primary);
        }
        /* Add zebra striping */
        .chat-message tbody tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        [data-theme="dark"] .chat-message tbody tr:nth-child(even) {
            background-color: rgba(255,255,255,0.03);
        }
        .chat-message img {
            max-width: 100%;
            height: auto;
            margin-top: 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            display: block;
        }
        
        /* Interactive chart styling */
        .chat-message .interactive-chart {
            width: 100%; /* Will be overridden by inline style */
            margin: 5px auto;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden !important; /* Keep content within rounded borders */
            background-color: var(--bg-primary);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            display: flex; /* Add flex display */
            align-items: center; /* Center vertically */
            justify-content: center; /* Center horizontally */
        }
        
        /* Ensure Plotly charts respect the rounded container */
        .chat-message .interactive-chart .plotly {
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* Fix Plotly's main plot area to have rounded corners */
        .chat-message .interactive-chart .plot-container .main-svg {
            border-radius: 10px;
        }
        
        /* Chart container - matched to standard message box */
        .chat-message.bot.chart-container {
            padding: 10px 15px; /* Same padding as regular chat message */
            max-width: 75%; /* Exactly the same as standard messages */
            margin-left: 0; 
            margin-right: 0;
            display: flex;
            flex-direction: column;
            align-items: center; /* Center the chart horizontally */
            justify-content: center; /* Center vertically too */
            overflow: visible;
        }
        
        /* Chart container should adjust its height based on content */
        .chat-message.bot.chart-container strong {
            align-self: flex-start;
        }
        
        /* The Plotly modebar should not be cut off */
        .js-plotly-plot .plotly .modebar {
            z-index: 10;
        }
        
        /* Ensure modebar buttons are visible in dark mode */
        [data-theme="dark"] .js-plotly-plot .plotly .modebar-btn path {
            fill: #ffffff;
        }
        
        /* Style for chart download button */
        .chart-download-btn {
            background-color: var(--accent-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
            margin-top: 8px;
            display: inline-block;
        }
        
        .chart-download-btn:hover {
            background-color: var(--accent-hover);
        }
        
        .message-input-area {
            display: flex;
            align-items: center;
            padding-top: 20px; /* Increased padding */
            border-top: 1px solid var(--border-color); /* Use main border color */
        }
        .message-input-area textarea {
            flex-grow: 1;
            padding: 14px 18px; /* Increased padding */
            border: 1px solid var(--border-color);
            border-radius: 25px; /* More rounded */
            margin-right: 12px;
            resize: none;
            min-height: 26px; /* Adjusted height */
            line-height: 1.6; /* Increased line height */
            font-family: 'Segoe UI', Roboto, sans-serif;
            font-size: 1em;
            outline: none;
            transition: border-color 0.2s ease, box-shadow 0.2s ease; /* Added box-shadow transition */
            background-color: var(--bg-secondary); /* Changed background */
            color: var(--text-primary);
        }
        .message-input-area textarea:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15); /* Adjusted shadow */
        }
        .message-input-area button {
            padding: 0; /* Remove padding for icon button */
            width: 50px; /* Fixed width */
            height: 50px; /* Fixed height */
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 50%; /* Circular button */
            cursor: pointer;
            font-size: 1.2em; /* Larger icon */
            transition: background-color 0.2s ease, transform 0.1s ease; /* Added transform for subtle pop */
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px var(--button-shadow);
        }
        .message-input-area button:hover {
            background-color: var(--accent-hover);
            transform: scale(1.05); /* Subtle pop on hover */
        }
        .message-input-area button:active {
            transform: scale(0.98); /* Press effect */
        }
        .footer {
            background-color: var(--bg-secondary);
            padding: 18px; /* Increased padding */
            text-align: center;
            border-top: 1px solid var(--border-color);
            font-size: 0.85em; /* Adjusted font size */
            color: var(--text-muted);
            box-shadow: 0 -1px 3px var(--header-shadow); /* Softer shadow */
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Vanna specific output areas */
        .vanna-output-container {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--bg-primary);
            transition: background-color 0.3s ease;
        }
        #error-message-container {
            color: #d9534f; /* Keep error color */
            background-color: #f8d7da; /* Bootstrap-like error background */
            border: 1px solid #f5c6cb; /* Bootstrap-like error border */
            display:none;
            padding: 15px; /* Increased padding */
            border-radius: 6px;
            margin-top: 15px; /* Ensure it has margin */
        } 
        #data-table-display-container { display:none; } 
        #plotly-chart-display-container {
             display:none;
             padding: 0;
             border: none;
        }
        
        #plotly-chart-display-container h4 {
            font-size: 1em;
            color: var(--text-secondary);
            margin-bottom: 10px;
            font-weight: 500;
        }

        /* Loading indicator */
        .is-loading { }

        .loading-shimmer-text {
            font-weight: 600;
            background-image: linear-gradient(
                -75deg,
                rgba(178, 178, 178, 0.8) 30%, /* Further increased contrast for dark mode */
                rgba(240, 240, 240, 0.95) 50%, /* Further increased contrast for dark mode */
                rgba(178, 178, 178, 0.8) 70%  /* Further increased contrast for dark mode */
            );
            background-size: 200% auto;
            color: transparent;
            background-clip: text;
            -webkit-background-clip: text;
            animation: textShimmer 1.8s linear infinite;
        }

        @keyframes textShimmer {
            to {
                background-position: -200% center;
            }
        }
        
        /* Menu toggle for mobile */
        .menu-toggle {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 100;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--accent-color);
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
        }
        
        /* Media queries for responsive design */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                padding: 12px 15px; /* Adjusted padding */
                align-items: flex-start;
            }
            
            .header-options {
                margin-top: 12px; /* Adjusted margin */
                display: flex;
                flex-wrap: wrap;
            }
            
            .header-options a, .header-options span, .theme-toggle {
                margin-left: 0;
                margin-right: 15px;
                margin-top: 5px;
            }
            
            .main-container {
                position: relative;
            }
            
            .nav-menu {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                width: 75%;
                max-width: 280px;
                z-index: 10;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .nav-menu.active {
                transform: translateX(0);
            }
            
            .menu-toggle {
                display: flex;
            }
            
            .chat-interface {
                padding: 20px; /* Adjusted padding */
            }
            
            .chat-message {
                max-width: 90%; /* Allow slightly wider messages on mobile */
            }
            
            .message-input-area {
                flex-direction: row; /* Keep as row for better alignment */
                align-items: center; /* Center items vertically */
            }
            
            .message-input-area textarea {
                margin-right: 10px; /* Add back some margin */
                margin-bottom: 0; /* Remove bottom margin if in row */
            }

            .message-input-area button {
                width: 45px; /* Slightly smaller on mobile */
                height: 45px;
                font-size: 1.1em;
            }
        }
    </style>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="header">
        <span class="header-title">HOSPITAL ASSISTANT SYSTEM</span>
        <div class="header-options">
            <a href="#"><i class="fas fa-globe"></i> EN</a> | <a href="#"><i class="fas fa-globe"></i> FR</a>
            <span><i class="fas fa-user-md"></i> Dr. Leblanc</span>
            <a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            <span class="theme-toggle" id="theme-toggle">
                <i class="fas fa-moon"></i> Toggle Theme
            </span>
        </div>
    </div>

    <div class="main-container">
        <div class="nav-menu" id="nav-menu">
            <h3><i class="fas fa-bars"></i> Navigation Menu</h3>
            <button id="nav-dashboard"><i class="fas fa-chart-line"></i> Dashboard</button>
            <button id="nav-chatbot" class="active"><i class="fas fa-comments"></i> Chatbot</button>
            <button id="nav-patient-search"><i class="fas fa-search"></i> Patient Search</button>

            <button id="nav-settings"><i class="fas fa-cog"></i> Settings</button>
        </div>

        <button class="menu-toggle" id="menu-toggle">
            <i class="fas fa-bars"></i>
        </button>

        <div class="chat-interface">
            <h2><i class="fas fa-robot"></i> Hospital Data Assistant</h2>
            <div class="chat-history" id="chat-history">
                <!-- Welcome message will be added by JavaScript -->
            </div>

            <!-- Vanna specific output areas -->
            <div id="error-message-container" class="vanna-output-container"></div>
            <div id="data-table-display-container" class="vanna-output-container">
                <h4>Data Table (Auxiliary):</h4>
                <div id="table-display"></div>
            </div>
            <div id="plotly-chart-display-container" class="vanna-output-container">
                <h4>Chart (Auxiliary):</h4>
                <div id="chart-display" style="width:100%; min-height:300px;"></div>
            </div>
            
            <div class="message-input-area">
                <textarea id="message-input" placeholder="Type your question about hospital data..." rows="1"></textarea>
                <button id="send-button" aria-label="Send Message"><i class="fas fa-paper-plane"></i></button> 
            </div>
        </div>
    </div>

    <div class="footer">
        &copy; 2077 Hospital Assistant System | <a href="#" id="privacy-link">Privacy Policy</a> | <a href="#" id="help-link">Help</a>
    </div>

    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
        let currentVannaId = null; 

            // Cache DOM elements
        const chatHistory = document.getElementById('chat-history');
        chatHistory.setAttribute('aria-live', 'polite'); // Add ARIA live region
        const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
        const errorMessageContainer = document.getElementById('error-message-container');
        const plotlyChartDisplayContainer = document.getElementById('plotly-chart-display-container');
        const chartDisplayElement = document.getElementById('chart-display');
            const themeToggle = document.getElementById('theme-toggle');
            const menuToggle = document.getElementById('menu-toggle');
            const navMenu = document.getElementById('nav-menu');

        let currentLoadingMessageDiv = null;
        let ellipsisInterval = null;

        // Variable to hold the current EventSource connection
        let currentEventSource = null;
        // Variable to hold the assistant's message div during streaming
        let streamingMessageDiv = null;
        let streamingContentSpan = null; // Span to hold the actual text content

        // Add a new variable at the top of the script
        let isCurrentlyThinking = false;
        let isProcessing = false; // To track overall processing state

            // Theme handling
            function setTheme(themeName) {
                document.documentElement.setAttribute('data-theme', themeName);
                localStorage.setItem('theme', themeName);
                
                // Update icon
                const themeIcon = themeToggle.querySelector('i');
                if (themeName === 'dark') {
                    themeIcon.className = 'fas fa-sun';
                } else {
                    themeIcon.className = 'fas fa-moon';
                }
            }
            
            // Check for saved theme preference or prefer-color-scheme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                setTheme(savedTheme);
            } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                setTheme('dark');
            }
            
            // Add event listener for theme toggle
            themeToggle.addEventListener('click', function() {
                const currentTheme = localStorage.getItem('theme') || 'light';
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                setTheme(newTheme);
                
                // Update any existing charts when theme changes
                document.querySelectorAll('.interactive-chart').forEach(chartDiv => {
                    if (chartDiv._Plotly) {
                        Plotly.relayout(chartDiv, {
                            template: newTheme === 'dark' ? 'plotly_dark' : 'plotly'
                        });
                    }
                });
            });
            
            // Handle scroll events that might affect chart sizing (debounced)
            let scrollTimeout;
            chatHistory.addEventListener('scroll', function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(function() {
                    document.querySelectorAll('.interactive-chart').forEach(chartDiv => {
                        // Check if the chart is visible in the viewport
                        const rect = chartDiv.getBoundingClientRect();
                        const isVisible = (
                            rect.top >= 0 &&
                            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
                        );
                        
                        // Only resize visible charts
                        if (isVisible && typeof chartDiv._resizeFunction === 'function') {
                            chartDiv._resizeFunction();
                        }
                    });
                }, 200);
            });
            
            // Handle window resize for responsive charts - more efficient approach
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    // This will handle all charts in the chat
                    document.querySelectorAll('.interactive-chart').forEach(chartDiv => {
                        if (chartDiv._resizeFunction && typeof chartDiv._resizeFunction === 'function') {
                            // Call the specific resize function for this chart
                            chartDiv._resizeFunction();
                        } else if (chartDiv._Plotly) {
                            // For charts without a specific resize function
                            const container = chartDiv.closest('.chat-message');
                            if (container) {
                                // Get available width
                                const availableWidth = container.clientWidth - 30;
                                const chartHeight = Math.min(availableWidth * 0.75, 400);
                                
                                // Update DOM element size
                                chartDiv.style.width = availableWidth + 'px';
                                chartDiv.style.height = chartHeight + 'px';
                                
                                // Update Plotly internal dimensions
                                Plotly.relayout(chartDiv, {
                                    width: availableWidth,
                                    height: chartHeight,
                                    'xaxis.automargin': true,
                                    'yaxis.automargin': true,
                                    autosize: true
                                });
                            }
                        }
                    });
                }, 150); // Reduced delay for better responsiveness
            });
            
            // Mobile menu toggle
            menuToggle.addEventListener('click', function() {
                navMenu.classList.toggle('active');
            });
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (navMenu.classList.contains('active') && 
                    !navMenu.contains(event.target) && 
                    event.target !== menuToggle) {
                    navMenu.classList.remove('active');
                }
            });
            
            // Add welcome message
            function addWelcomeMessage() {
                appendMessage("Welcome to the Hospital Data Assistant! Ask me questions about patient data, orders, or any other database information.", 'bot');
            }
            
            // Add initial welcome message
            addWelcomeMessage();
            
            // Attach event listeners
            sendButton.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault(); 
                    sendMessage();
                }
            });
            
            // Function to append message to chat
        function appendMessage(text, sender, isHtml = false) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('chat-message', sender);
            
            const strong = document.createElement('strong');
                strong.textContent = sender === 'user' ? 'You: ' : 'Assistant: '; 
            
            messageDiv.appendChild(strong);
            if (isHtml) {
                const span = document.createElement('span');
                span.innerHTML = text; 
                messageDiv.appendChild(span);
            } else {
                messageDiv.appendChild(document.createTextNode(text)); 
            }
            chatHistory.appendChild(messageDiv);
            // Add visible class after a short delay to trigger animation
            setTimeout(() => { messageDiv.classList.add('visible'); }, 50);
            chatHistory.scrollTop = chatHistory.scrollHeight; 
                
            return messageDiv;
        }

            // Function to reset auxiliary displays
        function resetAuxiliaryDisplays() {
            errorMessageContainer.style.display = 'none';
            errorMessageContainer.textContent = '';
            plotlyChartDisplayContainer.style.display = 'none';
            chartDisplayElement.innerHTML = '';
        }
        
            // Function to show loading indicator
        function showLoading(message) {
            if (ellipsisInterval) clearInterval(ellipsisInterval);

            // Remove the previous loading message div if it exists
            if (currentLoadingMessageDiv) {
                currentLoadingMessageDiv.remove();
                currentLoadingMessageDiv = null;
            }

            // Create and append the new loading message div
            currentLoadingMessageDiv = document.createElement('div');
                currentLoadingMessageDiv.id = 'dynamic-loading-indicator';
            currentLoadingMessageDiv.classList.add('chat-message', 'bot', 'is-loading');
            
            const baseTextSpan = document.createElement('span');
                baseTextSpan.id = 'loading-base-text';
                baseTextSpan.classList.add('loading-shimmer-text');
                baseTextSpan.textContent = message;
            currentLoadingMessageDiv.appendChild(baseTextSpan);
            
            chatHistory.appendChild(currentLoadingMessageDiv);
                chatHistory.scrollTop = chatHistory.scrollHeight;

                return currentLoadingMessageDiv;
            }
            
            // Function to hide loading indicator
        function hideLoading() {
                if (ellipsisInterval) clearInterval(ellipsisInterval);
            ellipsisInterval = null;
            if (currentLoadingMessageDiv) {
                currentLoadingMessageDiv.remove();
                currentLoadingMessageDiv = null;
            }
        }

            // Function to generate HTML table from records
        function generateTableHtml(records) {
            if (!records || records.length === 0) return '';
                
                const tableEl = document.createElement('table');
                tableEl.className = 'inline-chat-table';
                
                // Create header row
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');
                
            Object.keys(records[0]).forEach(key => {
                    const th = document.createElement('th');
                    th.textContent = key;
                    headerRow.appendChild(th);
            });
                
                thead.appendChild(headerRow);
                tableEl.appendChild(thead);
                
                // Create table body
                const tbody = document.createElement('tbody');
                
            records.forEach(record => {
                    const row = document.createElement('tr');
                    
                    Object.values(record).forEach(value => {
                        const td = document.createElement('td');
                        td.textContent = String(value);
                        row.appendChild(td);
                });
                    
                    tbody.appendChild(row);
            });
                
                tableEl.appendChild(tbody);
                
                // Convert the table element to HTML string
                const tempDiv = document.createElement('div');
                tempDiv.appendChild(tableEl);
                return tempDiv.innerHTML;
            }
            
            // Function to display error in chat
            function showErrorInChat(message) {
                appendMessage(`Error: ${message}`, 'bot');
        }

            // Main function to send message
        async function sendMessage() {
            const question = messageInput.value.trim();
            if (!question || isProcessing) return; // Don't send if empty or already processing

            isProcessing = true;
            sendButton.disabled = true;
            messageInput.disabled = true;
            sendButton.style.cursor = 'not-allowed';
            messageInput.style.cursor = 'not-allowed';

            appendMessage(question, 'user');
            messageInput.value = ''; 

            let apiUrl = `/api/v0/generate_sql?question=${encodeURIComponent(question)}`;
                if (currentVannaId) {
                apiUrl += `&existing_id=${currentVannaId}`;
                    // showLoading('Processing your follow-up...'); // Loading handled differently for stream
            } else {
                    // showLoading('Generating SQL or response...'); // Loading handled differently for stream
            }
                
            resetAuxiliaryDisplays();

            // --- Start of Streaming Logic ---
            if (currentEventSource) {
                currentEventSource.close(); // Close any existing connection
            }

            // Create the initial 'Assistant: ...' message shell
            // The actual content will be filled by the stream.
            streamingMessageDiv = document.createElement('div');
            streamingMessageDiv.classList.add('chat-message', 'bot');
            const strong = document.createElement('strong'); // This is the 'Assistant: ' prefix
            strong.textContent = 'Assistant: ';
            streamingMessageDiv.appendChild(strong);
            
            streamingContentSpan = document.createElement('span'); // Span to hold the actual text content
            streamingMessageDiv.appendChild(streamingContentSpan);

            chatHistory.appendChild(streamingMessageDiv);
            setTimeout(() => { streamingMessageDiv.classList.add('visible'); }, 50);
            chatHistory.scrollTop = chatHistory.scrollHeight;

            // Initial loading message for a new query, before any SSE events arrive
            if (strong) strong.style.display = 'none';
            streamingContentSpan.textContent = "Assistant is thinking...";
            streamingContentSpan.classList.add('loading-shimmer-text');
            firstChunkProcessed = false; 
            isCurrentlyThinking = false; 
            accumulatedResponse = ""; 

            currentEventSource = new EventSource(apiUrl); 

            currentEventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);

                // Always ensure input is disabled during any message processing step
                sendButton.disabled = true;
                messageInput.disabled = true;
                sendButton.style.cursor = 'not-allowed';
                messageInput.style.cursor = 'not-allowed';

                if (data.type === 'thinking_on') {
                    if (strong) strong.style.display = 'none'; 
                    streamingContentSpan.textContent = "Processing thoughts...";
                    streamingContentSpan.classList.remove('loading-shimmer-text'); 
                    streamingContentSpan.classList.add('llm-processing-thoughts'); 
                    firstChunkProcessed = true; 
                    isCurrentlyThinking = true;
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                } else if (data.type === 'thinking_off') {
                    if (isCurrentlyThinking) {
                        if (strong) strong.style.display = ''; 
                        streamingContentSpan.textContent = ''; 
                        streamingContentSpan.classList.remove('llm-processing-thoughts');
                    }
                    isCurrentlyThinking = false;
                } else if (data.type === 'chunk') {
                    if (isCurrentlyThinking || !firstChunkProcessed) {
                        if (strong) strong.style.display = ''; 
                        streamingContentSpan.textContent = ''; 
                        streamingContentSpan.classList.remove('loading-shimmer-text');
                        streamingContentSpan.classList.remove('llm-processing-thoughts');
                        isCurrentlyThinking = false;
                    }
                    if (!firstChunkProcessed) { 
                        if (strong) strong.style.display = ''; 
                        streamingContentSpan.classList.remove('loading-shimmer-text'); 
                        firstChunkProcessed = true;
                    }
                    accumulatedResponse += data.content;
                    streamingContentSpan.textContent = accumulatedResponse; 
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                } else if (data.type === 'sql_result') {
                    currentEventSource.close();
                    currentEventSource = null;
                    if (isCurrentlyThinking || !firstChunkProcessed) { // If we were thinking or haven't processed any chunk
                         if (strong) strong.style.display = ''; // Show prefix
                         streamingContentSpan.textContent = ''; // Clear any thinking/shimmer text
                         streamingContentSpan.classList.remove('loading-shimmer-text');
                         streamingContentSpan.classList.remove('llm-processing-thoughts');
                         isCurrentlyThinking = false;
                         firstChunkProcessed = true; // Mark as processed
                    }
                    currentVannaId = data.id;
                    
                    const fullLLMResponse = data.full_response; // Already cleaned by Python
                    
                    streamingContentSpan.classList.add('fading-out');
                    
                    setTimeout(() => {
                        streamingContentSpan.classList.remove('fading-out');
                        streamingContentSpan.innerHTML = "<strong>Extracted SQL:</strong><br>";
                        
                        const sqlDisplay = document.createElement('pre');
                        sqlDisplay.classList.add('sql-display');
                        const sqlCodeEl = document.createElement('code');
                        sqlCodeEl.textContent = data.text;
                        sqlDisplay.appendChild(sqlCodeEl);
                        streamingContentSpan.appendChild(sqlDisplay);
                        
                        const llmToggleButton = document.createElement('button');
                        llmToggleButton.classList.add('sql-toggle-button');
                        llmToggleButton.textContent = 'Show Full LLM Output';
                        streamingMessageDiv.appendChild(llmToggleButton);
                        
                        const llmContainer = document.createElement('div');
                        llmContainer.classList.add('sql-code-container');
                        
                        const llmPre = document.createElement('pre');
                        const llmCode = document.createElement('code');
                        llmCode.textContent = fullLLMResponse;
                        llmPre.appendChild(llmCode);
                        llmContainer.appendChild(llmPre);
                        streamingMessageDiv.appendChild(llmContainer);
                        
                        llmToggleButton.onclick = () => {
                            llmContainer.classList.toggle('expanded');
                            llmToggleButton.textContent = llmContainer.classList.contains('expanded') ? 'Hide Full LLM Output' : 'Show Full LLM Output';
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        };
                        
                        chatHistory.scrollTop = chatHistory.scrollHeight;
                        handleSqlResponse(data.id, data.text, streamingMessageDiv, streamingContentSpan);
                    }, 300); 
                    
                } else if (data.type === 'chat_result') {
                    currentEventSource.close();
                    currentEventSource = null;
                     if (isCurrentlyThinking || !firstChunkProcessed) { // If we were thinking or haven't processed any chunk
                         if (strong) strong.style.display = ''; // Show prefix
                         streamingContentSpan.textContent = ''; // Clear any thinking/shimmer text
                         streamingContentSpan.classList.remove('loading-shimmer-text');
                         streamingContentSpan.classList.remove('llm-processing-thoughts');
                         isCurrentlyThinking = false;
                         firstChunkProcessed = true; // Mark as processed
                    }
                    currentVannaId = data.id;
                    streamingContentSpan.textContent = data.message; // Already cleaned by Python
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                    // This is a final state for chat_result, re-enable input
                    isProcessing = false;
                    sendButton.disabled = false;
                    messageInput.disabled = false;
                    sendButton.style.cursor = 'pointer';
                    messageInput.style.cursor = 'auto';
                } else if (data.type === 'error') { 
                    currentEventSource.close();
                    currentEventSource = null;
                    if (isCurrentlyThinking || !firstChunkProcessed) {
                         if (strong) strong.style.display = ''; 
                         streamingContentSpan.classList.remove('loading-shimmer-text');
                         streamingContentSpan.classList.remove('llm-processing-thoughts');
                         isCurrentlyThinking = false;
                         firstChunkProcessed = true;
                    }
                    
                    if (streamingContentSpan) { 
                        streamingContentSpan.textContent = "Error processing request.";
                        
                        const errorToggleButton = document.createElement('button');
                        errorToggleButton.classList.add('sql-toggle-button');
                        errorToggleButton.textContent = 'Show Error Details';
                        streamingMessageDiv.appendChild(errorToggleButton);
                        
                        const errorContainer = document.createElement('div');
                        errorContainer.classList.add('sql-code-container');
                        
                        const errorPre = document.createElement('pre');
                        const errorCode = document.createElement('code');
                        errorCode.textContent = data.error + (data.id ? `\\n\\nContext ID: ${data.id}` : '');
                        errorPre.appendChild(errorCode);
                        errorContainer.appendChild(errorPre);
                        streamingMessageDiv.appendChild(errorContainer);
                        
                        errorToggleButton.onclick = () => {
                            errorContainer.classList.toggle('expanded');
                            errorToggleButton.textContent = errorContainer.classList.contains('expanded') ? 'Hide Error Details' : 'Show Error Details';
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        };
                    } else { 
                        appendMessage("Error processing request.",'bot');
                    }
                    // Error state, re-enable input
                    isProcessing = false;
                    sendButton.disabled = false;
                    messageInput.disabled = false;
                    sendButton.style.cursor = 'pointer';
                    messageInput.style.cursor = 'auto';
                }
            };

            currentEventSource.onerror = function(error) {
                console.error("EventSource failed:", error);
                currentEventSource.close();
                currentEventSource = null;
                
                if (streamingContentSpan && !firstChunkProcessed) {
                    if (strong) strong.style.display = ''; 
                    streamingContentSpan.classList.remove('loading-shimmer-text');
                    streamingContentSpan.classList.remove('llm-processing-thoughts');
                    streamingContentSpan.textContent = "Failed to get response from server.";
                    if (!streamingMessageDiv.classList.contains('visible')) {
                        streamingMessageDiv.classList.add('visible');
                    }
                } else if (streamingContentSpan && accumulatedResponse) { 
                    if (strong) strong.style.display = '';
                    streamingContentSpan.textContent = "Response was interrupted. Partial content available.";
                    streamingContentSpan.classList.remove('llm-processing-thoughts'); // Ensure no thinking style
                    
                    const errorToggleButton = document.createElement('button');
                    errorToggleButton.classList.add('sql-toggle-button');
                    errorToggleButton.textContent = 'Show Partial Response';
                    streamingMessageDiv.appendChild(errorToggleButton);
                    
                    const errorContainer = document.createElement('div');
                    errorContainer.classList.add('sql-code-container');
                    
                    const errorPre = document.createElement('pre');
                    const errorCode = document.createElement('code');
                    errorCode.textContent = accumulatedResponse + "\\n\\n(Error: Connection lost before full response)";
                    errorPre.appendChild(errorCode);
                    errorContainer.appendChild(errorPre);
                    streamingMessageDiv.appendChild(errorContainer);
                    
                    errorToggleButton.onclick = () => {
                        errorContainer.classList.toggle('expanded');
                        errorToggleButton.textContent = errorContainer.classList.contains('expanded') ? 'Hide Partial Response' : 'Show Partial Response';
                        chatHistory.scrollTop = chatHistory.scrollHeight;
                    };
                } else {
                    appendMessage("Failed to get response from server.", 'bot');
                }
                
                if (streamingMessageDiv && !streamingMessageDiv.classList.contains('visible')){
                    streamingMessageDiv.classList.add('visible');
                }
            };
            // --- End of Streaming Logic ---
        }
        
        // New function to handle the workflow after SQL is identified from stream
        async function handleSqlResponse(vannaId, sqlText, streamingMessageDiv, streamingContentSpan) {
            // The SQL is already displayed by the streaming logic (streamingContentSpan).
            // We will append a status message to streamingMessageDiv for execution attempts.
            // Ensure input is still disabled
            sendButton.disabled = true;
            messageInput.disabled = true;
            sendButton.style.cursor = 'not-allowed';
            messageInput.style.cursor = 'not-allowed';

            let executionStatusDiv = document.createElement('div');
            executionStatusDiv.id = 'execution-status-' + vannaId;
            executionStatusDiv.classList.add('execution-status', 'is-loading');
            executionStatusDiv.innerHTML = `<span class="loading-shimmer-text">Attempting to execute SQL...</span>`;
            streamingMessageDiv.appendChild(executionStatusDiv);
            chatHistory.scrollTop = chatHistory.scrollHeight;

            try {
                const runSqlResponse = await fetch(`/api/v0/run_sql?id=${vannaId}`);
                const runSqlData = await runSqlResponse.json();
                
                executionStatusDiv.remove(); // Remove "Attempting to execute SQL..."

                if (runSqlData.type === "error") {
                    // Case: SQL execution failed (either initial or corrected if an attempt was made by backend)
                    if (runSqlData.llm_explanation) {
                        // LLM provided an explanation instead of corrected SQL
                        appendMessage(`<strong>SQL Execution Failed:</strong> ${runSqlData.llm_explanation}`, 'bot', true);
                    } else if (runSqlData.attempted_corrected_sql) {
                        // Corrected SQL was attempted and also failed
                        streamingContentSpan.innerHTML = `<strong>Initial SQL (Failed):</strong><br><pre class="sql-display"><code>${runSqlData.failed_sql}</code></pre>` +
                                                       `<strong>Attempted Corrected SQL (Also Failed):</strong><br><pre class="sql-display"><code>${runSqlData.attempted_corrected_sql}</code></pre>`;
                        appendMessage(`<strong>SQL Execution Failed:</strong> Both initial and corrected SQL queries failed. Error: ${runSqlData.error}`, 'bot', true);
                    } else {
                        // Generic initial SQL failure
                        appendMessage(`<strong>SQL Execution Failed:</strong> ${runSqlData.error}<br>Failed SQL:<pre class="sql-display"><code>${runSqlData.failed_sql || sqlText}</code></pre>`, 'bot', true);
                    }
                    // Error in SQL execution, re-enable input
                    isProcessing = false;
                    sendButton.disabled = false;
                    messageInput.disabled = false;
                    sendButton.style.cursor = 'pointer';
                    messageInput.style.cursor = 'auto';
                    return;
                }
                
                // SQL Execution was successful (either initial or corrected)
                const dfRecords = JSON.parse(runSqlData.df);

                if (runSqlData.corrected_sql_executed) {
                    // Update the original message bubble to show both failed and corrected SQL
                    streamingContentSpan.innerHTML = `<strong>Initial SQL (Failed):</strong><br><pre class="sql-display"><code>${runSqlData.original_sql}</code></pre>` +
                                                   `<strong>Corrected SQL (Executed Successfully):</strong><br><pre class="sql-display"><code>${runSqlData.corrected_sql_executed}</code></pre>`;
                    appendMessage("The original SQL query failed. A corrected version was generated and executed successfully.", 'bot');
                } else {
                    // Initial SQL was successful, no message needed here as SQL is already shown.
                    // Just ensure the status message is gone.
                }

                if (dfRecords.length > 0) {
                    const tableHtml = generateTableHtml(dfRecords);
                    appendMessage("Data retrieved:<br>" + tableHtml, 'bot', true);
                    
                    // Create streaming summary message
                    let summaryMessageDiv = document.createElement('div');
                    summaryMessageDiv.classList.add('chat-message', 'bot');
                    let summaryStrong = document.createElement('strong');
                    summaryStrong.textContent = 'Summary: ';
                    summaryMessageDiv.appendChild(summaryStrong);
                    
                    let summaryContentSpan = document.createElement('span');
                    summaryContentSpan.textContent = "Generating summary...";
                    summaryContentSpan.classList.add('loading-shimmer-text');
                    summaryMessageDiv.appendChild(summaryContentSpan);
                    
                    chatHistory.appendChild(summaryMessageDiv);
                    setTimeout(() => { summaryMessageDiv.classList.add('visible'); }, 50);
                    chatHistory.scrollTop = chatHistory.scrollHeight;

                    // Initialize streaming variables for summary
                    summaryStrong.style.display = 'none';
                    let summaryFirstChunkProcessed = false;
                    let summaryIsThinking = false;
                    let summaryAccumulated = "";

                    // Create EventSource for streaming summary
                    let summaryEventSource = new EventSource(`/api/v0/generate_summary_stream?id=${vannaId}`);
                    
                    summaryEventSource.onmessage = function(event) {
                        const data = JSON.parse(event.data);

                        // Ensure input remains disabled during summary streaming
                        sendButton.disabled = true;
                        messageInput.disabled = true;
                        sendButton.style.cursor = 'not-allowed';
                        messageInput.style.cursor = 'not-allowed';

                        if (data.type === 'thinking_on') {
                            summaryStrong.style.display = 'none';
                            summaryContentSpan.textContent = "Processing thoughts...";
                            summaryContentSpan.classList.remove('loading-shimmer-text');
                            summaryContentSpan.classList.add('llm-processing-thoughts');
                            summaryFirstChunkProcessed = true;
                            summaryIsThinking = true;
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        } else if (data.type === 'thinking_off') {
                            if (summaryIsThinking) {
                                summaryStrong.style.display = '';
                                summaryContentSpan.textContent = '';
                                summaryContentSpan.classList.remove('loading-shimmer-text');
                                summaryContentSpan.classList.remove('llm-processing-thoughts');
                            }
                            summaryIsThinking = false;
                        } else if (data.type === 'summary_chunk') {
                            if (summaryIsThinking || !summaryFirstChunkProcessed) {
                                summaryStrong.style.display = '';
                                summaryContentSpan.textContent = '';
                                summaryContentSpan.classList.remove('loading-shimmer-text');
                                summaryContentSpan.classList.remove('llm-processing-thoughts');
                                summaryIsThinking = false;
                            }
                            if (!summaryFirstChunkProcessed) {
                                summaryStrong.style.display = '';
                                summaryContentSpan.classList.remove('loading-shimmer-text');
                                summaryFirstChunkProcessed = true;
                            }
                            summaryAccumulated += data.content;
                            summaryContentSpan.textContent = summaryAccumulated;
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        } else if (data.type === 'summary_result') {
                            summaryEventSource.close();
                            if (summaryIsThinking || !summaryFirstChunkProcessed) {
                                summaryStrong.style.display = '';
                                summaryContentSpan.textContent = '';
                                summaryContentSpan.classList.remove('loading-shimmer-text');
                                summaryContentSpan.classList.remove('llm-processing-thoughts');
                                summaryIsThinking = false;
                                summaryFirstChunkProcessed = true;
                            }
                            // Use the final summary from the server
                            summaryContentSpan.textContent = data.summary;
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                            // Summary is done, but chart generation might follow. Don't re-enable yet.
                        } else if (data.type === 'error') {
                            summaryEventSource.close();
                            if (summaryIsThinking || !summaryFirstChunkProcessed) {
                                summaryStrong.style.display = '';
                                summaryContentSpan.classList.remove('loading-shimmer-text');
                                summaryContentSpan.classList.remove('llm-processing-thoughts');
                                summaryIsThinking = false;
                                summaryFirstChunkProcessed = true;
                            }
                            summaryContentSpan.textContent = "Could not generate summary.";
                            console.warn("Summary streaming error:", data.error);
                            // Error in summary, re-enable here ONLY if no chart generation follows
                            // Or rely on chart generation to re-enable
                        }
                    };

                    summaryEventSource.onerror = function(error) {
                        console.error("Summary EventSource failed:", error);
                        summaryEventSource.close();
                        // Error in summary, re-enable here ONLY if no chart generation follows
                        // Or rely on chart generation to re-enable
                        
                        if (!summaryFirstChunkProcessed) {
                            summaryStrong.style.display = '';
                            summaryContentSpan.classList.remove('loading-shimmer-text');
                            summaryContentSpan.classList.remove('llm-processing-thoughts');
                            summaryContentSpan.textContent = "Failed to generate summary.";
                        }
                    };

                    // Wait for summary to complete before starting chart generation
                    summaryEventSource.addEventListener('message', function(event) {
                        const data = JSON.parse(event.data);
                        if (data.type === 'summary_result' || data.type === 'error') {
                            // Summary is complete, now start chart generation
                            setTimeout(() => {
                                startChartGeneration(vannaId); // Pass vannaId
                            }, 500);
                        }
                    });

                    function startChartGeneration(currentId) { // Accept vannaId as currentId
                        // Start chart generation
                        let chartPlaceholder = appendMessage('', 'bot');
                        let chartStrongTag = chartPlaceholder.querySelector('strong');
                        if (chartStrongTag) chartStrongTag.style.display = 'none';
                        let chartTextSpan = document.createElement('span');
                        chartTextSpan.textContent = "Generating chart...";
                        chartTextSpan.classList.add('loading-shimmer-text');
                        chartPlaceholder.appendChild(chartTextSpan);
                        chartPlaceholder.classList.add('visible');
                        chatHistory.scrollTop = chatHistory.scrollHeight;

                        (async () => {
                            try {
                                const plotResponse = await fetch(`/api/v0/generate_plotly_figure?id=${currentId}`);
                                const plotData = await plotResponse.json();
                                chartPlaceholder.remove();

                                if (plotData.type === "plotly_figure" && plotData.fig) {
                                    const chartMessageDiv = appendMessage('', 'bot');
                                    chartMessageDiv.classList.add('chart-container'); // Add chart-container class
                                    
                                    // Set chart container to match other message boxes
                                    chartMessageDiv.style.width = ''; // Let CSS handle the width
                                    
                                    // Remove the Assistant: prefix for chart containers
                                    const strongTag = chartMessageDiv.querySelector('strong');
                                    if (strongTag) strongTag.style.display = 'none';
                                    
                                    // Get the plotly figure ready
                                    let fig;
                                    try {
                                        fig = JSON.parse(plotData.fig);
                                    } catch (error) {
                                        console.error("Failed to parse plot JSON:", error);
                                        chartMessageDiv.textContent = "Error parsing chart data.";
                                        return;
                                    }
                                    
                                    // Add the chart div to the DOM first
                                    const chartDiv = document.createElement('div');
                                    chartDiv.className = 'interactive-chart';
                                    chartMessageDiv.appendChild(chartDiv);
                                    chartMessageDiv.classList.add('visible');
                                    
                                    // Function to properly size the chart
                                    const resizeChartToFit = () => {
                                        // Get the current message width
                                        const availableWidth = chartMessageDiv.clientWidth - 30; // Padding adjustment
                                        
                                        // Calculate appropriate height based on chart type
                                        let aspectRatio = 0.6; // Default aspect ratio
                                        
                                        // Try to determine chart type for better aspect ratio
                                        if (fig && fig.data && fig.data.length > 0) {
                                            const chartType = fig.data[0].type;
                                            // Pie charts look better with 1:1 ratio
                                            if (chartType === 'pie' || chartType === 'sunburst' || chartType === 'treemap') {
                                                aspectRatio = 1.0; // Square for pie charts
                                            }
                                            // Bar charts often need more height
                                            else if (chartType === 'bar' && fig.data[0].orientation === 'h') {
                                                aspectRatio = 0.8; // Taller for horizontal bar charts
                                            }
                                            // Line charts can be flatter
                                            else if (chartType === 'scatter' || chartType === 'line') {
                                                aspectRatio = 0.5; // Wider for line/scatter plots
                                            }
                                        }
                                        
                                        const chartHeight = Math.min(availableWidth * aspectRatio, 400);
                                        
                                        // Set dimensions
                                        chartDiv.style.width = availableWidth + 'px';
                                        chartDiv.style.height = chartHeight + 'px';
                                        
                                        // If Plotly is already initialized, update its layout
                                        if (chartDiv._Plotly) {
                                            Plotly.relayout(chartDiv, {
                                                width: availableWidth,
                                                height: chartHeight,
                                                'xaxis.automargin': true,
                                                'yaxis.automargin': true,
                                                autosize: true
                                            });
                                        }
                                    };
                                    
                                    // Store the resize function for later use
                                    chartDiv._resizeFunction = resizeChartToFit;
                                    
                                    // Wait for the DOM to update and measure
                                    setTimeout(() => {
                                        // Configure layout for proper display
                                        if (!fig.layout) fig.layout = {};
                                        fig.layout.autosize = true;
                                        
                                        // Call the resize function to calculate dimensions first
                                        resizeChartToFit();
                                        
                                        // Use the calculated dimensions
                                        fig.layout.width = chartDiv.clientWidth;
                                        fig.layout.height = chartDiv.clientHeight;
                                        
                                        // Optimize margins for smaller container
                                        fig.layout.margin = {l: 50, r: 30, t: 50, b: 50, pad: 4};
                                        
                                        // Make sure text is sized appropriately
                                        if (!fig.layout.font) fig.layout.font = {};
                                        fig.layout.font.size = 12; // Slightly larger fonts for readability
                                        
                                        // Adjust title layout
                                        fig.layout.title = {
                                            text: fig.layout.title?.text || '',
                                            font: { size: 14 },
                                            x: 0.5,
                                            y: 0.95
                                        };
                                        
                                        try {
                                            // Wait for Plotly to be defined with a timeout
                                            let attempts = 0;
                                            const maxAttempts = 50; // Try for up to 5 seconds (50 * 100ms)
                                            const intervalId = setInterval(function() {
                                                if (typeof Plotly !== 'undefined') {
                                                    clearInterval(intervalId);
                                                    Plotly.newPlot(chartDiv, fig.data, fig.layout, {
                                                        responsive: true,
                                                        displayModeBar: true,
                                                        displaylogo: false,
                                                        modeBarButtonsToRemove: ['lasso2d', 'select2d', 'zoomIn2d', 'zoomOut2d', 'autoScale2d', 'toggleSpikelines'],
                                                        template: document.documentElement.getAttribute('data-theme') === 'dark' ? 'plotly_dark' : 'plotly',
                                                        scrollZoom: false, // Disable scroll zoom for better user experience in chat
                                                        staticPlot: false, // Allow basic interactivity
                                                    });
                                            
                                                    // Force a resize after rendering to ensure proper fit
                                                    setTimeout(resizeChartToFit, 100);
                                                    
                                                    // Add a second resize to catch any rendering issues
                                                    setTimeout(resizeChartToFit, 500);
                                                    
                                                    // Ensure we scroll to show the chart after final resize
                                                    setTimeout(() => {
                                                        chatHistory.scrollTop = chatHistory.scrollHeight;
                                                    }, 550);
                                                    
                                                    // Listen for zoom events in the browser
                                                    window.addEventListener('resize', function() {
                                                        resizeChartToFit();
                                                    });
                                                    
                                                    // Mark this chart as having its own resize listener
                                                    chartDiv._resizeListenerAdded = true;
                                                    
                                                    // Create a ResizeObserver to watch for container size changes
                                                    if (window.ResizeObserver) {
                                                        const resizeObserver = new ResizeObserver(function() {
                                                            resizeChartToFit();
                                                        });
                                                        resizeObserver.observe(chartMessageDiv);
                                                    }
                                                    
                                                    // Add a download button below the chart
                                                    const downloadBtn = document.createElement('button');
                                                    downloadBtn.textContent = 'Download Chart';
                                                    downloadBtn.className = 'chart-download-btn';
                                                    downloadBtn.style.marginTop = '10px';
                                                    downloadBtn.onclick = async () => {
                                                        try {
                                                            const imgDataUrl = await Plotly.toImage(chartDiv, {
                                                                format: 'png', 
                                                                width: Math.min(chartDiv.clientWidth * 2, 1200),
                                                                height: Math.min(chartDiv.clientHeight * 2, 800)
                                                            });
                                                            const downloadLink = document.createElement('a');
                                                            downloadLink.href = imgDataUrl;
                                                            downloadLink.download = `chart-${currentId}.png`;
                                                            document.body.appendChild(downloadLink);
                                                            downloadLink.click();
                                                            document.body.removeChild(downloadLink);
                                                        } catch (err) {
                                                            console.error("Error downloading chart:", err);
                                                            alert("Failed to download chart");
                                                        }
                                                    };
                                                    chartMessageDiv.appendChild(downloadBtn);
                                                } else {
                                                    attempts++;
                                                    if (attempts >= maxAttempts) {
                                                        clearInterval(intervalId);
                                                        console.error("Plotly did not load in time.");
                                                        chartDiv.textContent = "Error rendering chart: Plotly library did not load.";
                                                    }
                                                }
                                            }, 100);
                                        } catch (plotlyError) {
                                            console.error("Plotly rendering error:", plotlyError);
                                            chartDiv.textContent = "Error rendering chart: " + plotlyError.message;
                                        }
                                    }, 200); // Longer delay to ensure DOM is ready
                                } else if (plotData.type === "info") {
                                    appendMessage(plotData.message, 'bot');
                                    // If there's an info message about why a chart wasn't generated,
                                    // display it in a more user-friendly way

                                } else if (plotData.type === "error") {
                                    console.warn("Plot error:", plotData.error);
                                    appendMessage("Could not generate a chart for this data.", 'bot');
                                } else if (plotData.type === "no_chart") {
                                    // LLM decided chart is not beneficial for this data
                                    const message = plotData.message || "Chart not recommended for this type of data.";
                                    appendMessage(message, 'bot');
                                } else {
                                    appendMessage("Chart could not be generated at this time.", 'bot');
                                }
                            } catch (error) {
                                chartPlaceholder.remove();
                                console.error("Fetch chart error:", error);
                                appendMessage("Error fetching chart.", 'bot');
                            } finally {
                                // Chart generation done (success or fail), re-enable input
                                isProcessing = false;
                                sendButton.disabled = false;
                                messageInput.disabled = false;
                                sendButton.style.cursor = 'pointer';
                                messageInput.style.cursor = 'auto';
                            }
                        })();
                    }
                } else {
                    const noResultsMessage = runSqlData.message || "No results found for this query.";
                    appendMessage(noResultsMessage, 'bot');
                    // No data, so no summary or chart. Re-enable input.
                    isProcessing = false;
                    sendButton.disabled = false;
                    messageInput.disabled = false;
                    sendButton.style.cursor = 'pointer';
                    messageInput.style.cursor = 'auto';
                }
            } catch (error) {
                console.error("Error running SQL or processing results:", error);
                appendMessage("An error occurred while running the SQL query or processing its results.", 'bot');
                // Uncaught error in SQL handling, re-enable input
                isProcessing = false;
                sendButton.disabled = false;
                messageInput.disabled = false;
                sendButton.style.cursor = 'pointer';
                messageInput.style.cursor = 'auto';
            }
        }
        
        // Handle suggestions in the UI
        document.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                messageInput.value = this.textContent;
                sendMessage();
            });
        });
        
        // Privacy policy and help links
        document.getElementById('privacy-link').addEventListener('click', function(e) {
            e.preventDefault();
            appendMessage("Our privacy policy ensures all patient data is handled securely according to HIPAA regulations. All queries are encrypted and no personal health information is stored in logs.", 'bot');
        });
        
        document.getElementById('help-link').addEventListener('click', function(e) {
            e.preventDefault();
            appendMessage("Need help? You can ask questions about patient data, hospital statistics, order information, and more.", 'bot');
        });

        // Function to add a visual "typing" or "thinking" indicator
        function showAssistantThinking(messageDiv, contentSpan) {
            const strongPrefix = messageDiv.querySelector('strong');
            if (strongPrefix) strongPrefix.style.display = 'none'; // Hide "Assistant:"
            contentSpan.textContent = "Assistant is thinking...";
            contentSpan.classList.add('loading-shimmer-text');
            contentSpan.classList.remove('llm-processing-thoughts');
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        // Function to clear assistant thinking/typing indicator and reveal content
        function clearAssistantThinking(messageDiv, contentSpan, showPrefix = true) {
            const strongPrefix = messageDiv.querySelector('strong');
            if (strongPrefix && showPrefix) strongPrefix.style.display = ''; // Show "Assistant:"
            contentSpan.textContent = ''; // Clear placeholder text
            contentSpan.classList.remove('loading-shimmer-text');
            contentSpan.classList.remove('llm-processing-thoughts');
        }

        // Adjustments for navigation menu buttons click event (example for Chatbot)
        const navChatbotButton = document.getElementById('nav-chatbot');
        if (navChatbotButton) {
            navChatbotButton.addEventListener('click', () => {
                window.location.href = 'index.html'; // Stay on index.html for chatbot
            });
        }

        const navDashboardButton = document.getElementById('nav-dashboard');
        if (navDashboardButton) {
            navDashboardButton.addEventListener('click', () => {
                window.location.href = 'dashboard.html';
            });
        }

        const navPatientSearchButton = document.getElementById('nav-patient-search');
        if (navPatientSearchButton) {
            navPatientSearchButton.addEventListener('click', () => {
                window.location.href = 'patient_search.html';
            });
        }



        const navSettingsButton = document.getElementById('nav-settings');
        if (navSettingsButton) {
            navSettingsButton.addEventListener('click', () => {
                window.location.href = 'settings.html';
            });
        }
    });
</script>
</body>
</html>
