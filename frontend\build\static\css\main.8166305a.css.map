{"version": 3, "file": "static/css/main.8166305a.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,uFAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EACpB,2BAAmB,CAAnB,yBAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,SAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,sBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,yCAAmB,CAAnB,+NAAmB,CAAnB,mCAAmB,CAAnB,uCAAmB,CAAnB,2NAAmB,CAAnB,sCAAmB,CAAnB,wMAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,8GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,oEAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,yCAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gDAAmB,CAAnB,iDAAmB,CAAnB,wBAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,kCAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,sBAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,qCAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,wLAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAGnB,MACE,oBAAqB,CACrB,mBAAuB,CACvB,gBAAiB,CACjB,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CACrB,sBAAuB,CACvB,sBAAuB,CACvB,sBAAuB,CACvB,qBAAsB,CACtB,qBAAsB,CACtB,gBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,yBAAiC,CACjC,yBACF,CAGA,MACE,oBAAqB,CACrB,sBAAuB,CACvB,gBAAiB,CACjB,sBAAuB,CACvB,wBAAyB,CACzB,iBAAqB,CACrB,sBAAuB,CACvB,sBAAuB,CACvB,sBAAuB,CACvB,qBAAsB,CACtB,qBAAsB,CACtB,mBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,yBAAgC,CAChC,yBACF,CAGA,KAGE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAmC,CAAnC,kCAAmC,CACnC,aAA0B,CAA1B,yBAA0B,CAJ1B,uFAAqG,CADrG,QAAS,CAMT,mDACF,CAGA,oBACE,SACF,CAEA,0BACE,gBACF,CAEA,0BACE,wBAAqC,CAArC,oCAAqC,CACrC,iBACF,CAEA,gCACE,wBAAqC,CAArC,oCACF,CAGA,sBAYE,0CAA2C,CAF3C,oBAAqB,CACrB,4BAA6B,CAT7B,kFAKC,CACD,yBAA0B,CAC1B,WAAkB,CARlB,eAYF,CAEA,uBACE,GACE,yBACF,CACF,CAGA,yBAEE,aAAwB,CAAxB,uBAAwB,CADxB,iBAAkB,CAElB,UACF,CAGA,cAGE,sCAAwC,CAFxC,SAAU,CACV,0BAEF,CAEA,kBACE,GACE,SAAU,CACV,uBACF,CACF,CAGA,oBACE,YAAa,CAEb,SAAU,CADV,eAAgB,CAEhB,gHACF,CAEA,6BAGE,cAAe,CAFf,gBAAiB,CACjB,SAAU,CAEV,YACF,CAGA,mBAGE,wBAAmC,CAAnC,kCAAmC,CAFnC,kBAAmB,CACnB,yBAA2B,CAE3B,uBACF,CAGA,yBACE,eACE,YACF,CACF,CAhJA,sDAiJA,CAjJA,sPAiJA,CAjJA,oDAiJA,CAjJA,yCAiJA,CAjJA,iBAiJA,CAjJA,6LAiJA,CAjJA,mDAiJA,CAjJA,oBAiJA,CAjJA,wDAiJA,CAjJA,2CAiJA,CAjJA,wBAiJA,CAjJA,sDAiJA,CAjJA,2CAiJA,CAjJA,wBAiJA,CAjJA,wDAiJA,CAjJA,2CAiJA,CAjJA,wBAiJA,CAjJA,wDAiJA,CAjJA,2CAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,4CAiJA,CAjJA,wBAiJA,CAjJA,sDAiJA,CAjJA,0CAiJA,CAjJA,wBAiJA,CAjJA,sDAiJA,CAjJA,+CAiJA,CAjJA,aAiJA,CAjJA,6CAiJA,CAjJA,+CAiJA,CAjJA,aAiJA,CAjJA,4CAiJA,CAjJA,8CAiJA,CAjJA,aAiJA,CAjJA,6CAiJA,CAjJA,uFAiJA,CAjJA,iGAiJA,CAjJA,+CAiJA,CAjJA,kGAiJA,CAjJA,mDAiJA,CAjJA,oBAiJA,CAjJA,uDAiJA,CAjJA,+HAiJA,CAjJA,wGAiJA,CAjJA,uEAiJA,CAjJA,wFAiJA,CAjJA,+CAiJA,CAjJA,wDAiJA,CAjJA,yCAiJA,CAjJA,gBAiJA,CAjJA,6LAiJA,CAjJA,iDAiJA,CAjJA,wBAiJA,CAjJA,uDAiJA,CAjJA,iDAiJA,CAjJA,wBAiJA,CAjJA,wDAiJA,CAjJA,sFAiJA,CAjJA,oBAiJA,CAjJA,qDAiJA,CAjJA,wDAiJA,CAjJA,oBAiJA,CAjJA,sDAiJA,CAjJA,wDAiJA,CAjJA,oBAiJA,CAjJA,qDAiJA,CAjJA,wDAiJA,CAjJA,oBAiJA,CAjJA,qDAiJA,CAjJA,yDAiJA,CAjJA,oBAiJA,CAjJA,sDAiJA,CAjJA,uDAiJA,CAjJA,oBAiJA,CAjJA,sDAiJA,CAjJA,gDAiJA,CAjJA,wBAiJA,CAjJA,sDAiJA,CAjJA,6DAiJA,CAjJA,gDAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,gDAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,gDAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,gDAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,iDAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,+CAiJA,CAjJA,wBAiJA,CAjJA,sDAiJA,CAjJA,oDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,oDAiJA,CAjJA,aAiJA,CAjJA,8CAiJA,CAjJA,oDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,oDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,oDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,oDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,qDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,qDAiJA,CAjJA,aAiJA,CAjJA,8CAiJA,CAjJA,sDAiJA,CAjJA,aAiJA,CAjJA,8CAiJA,CAjJA,sDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,mDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,mDAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,qEAiJA,CAjJA,oBAiJA,CAjJA,wDAiJA,CAjJA,6DAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,6DAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,6DAiJA,CAjJA,wBAiJA,CAjJA,qDAiJA,CAjJA,iEAiJA,CAjJA,aAiJA,CAjJA,8CAiJA,CAjJA,iEAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,gEAiJA,CAjJA,aAiJA,CAjJA,+CAiJA,CAjJA,mDAiJA,EAjJA,wDAiJA,CAjJA,sBAiJA,CAjJA,wBAiJA,CAjJA,uCAiJA,CAjJA,6LAiJA,CAjJA,8DAiJA,CAjJA,8DAiJA,CAjJA,8DAiJA,CAjJA,gCAiJA,CAjJA,oCAiJA,CAjJA,kDAiJA,CAjJA,mEAiJA,CAjJA,sGAiJA,EAjJA,mEAiJA,CAjJA,yCAiJA,CAjJA,8DAiJA,CAjJA,8DAiJA", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* CSS Custom Properties for Theme Variables */\n:root {\n  --bg-primary: #f8f9fa;\n  --bg-secondary: #ffffff;\n  --bg-nav: #eef2f5;\n  --text-primary: #212529;\n  --text-secondary: #495057;\n  --text-muted: #6c757d;\n  --accent-color: #007bff;\n  --accent-hover: #0056b3;\n  --border-color: #dee2e6;\n  --soft-border: #e9ecef;\n  --user-bubble: #007bff;\n  --user-text: #ffffff;\n  --bot-bubble: #e9ecef;\n  --bot-text: #212529;\n  --header-shadow: rgba(0,0,0,0.03);\n  --button-shadow: rgba(0, 0, 0, 0.05);\n}\n\n/* Dark Theme */\n.dark {\n  --bg-primary: #121212;\n  --bg-secondary: #1e1e1e;\n  --bg-nav: #242424;\n  --text-primary: #e0e0e0;\n  --text-secondary: #b0b0b0;\n  --text-muted: #888888;\n  --accent-color: #0d6efd;\n  --accent-hover: #3b82f6;\n  --border-color: #343a40;\n  --soft-border: #2c2f33;\n  --user-bubble: #0d6efd;\n  --user-text: #f0f0f0;\n  --bot-bubble: #343a40;\n  --bot-text: #e0e0e0;\n  --header-shadow: rgba(0,0,0,0.1);\n  --button-shadow: rgba(255, 255, 255, 0.05);\n}\n\n/* Base styles */\nbody {\n  margin: 0;\n  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: var(--bg-primary);\n  color: var(--text-primary);\n  transition: background-color 0.3s ease, color 0.3s ease;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: var(--border-color);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background-color: var(--accent-color);\n}\n\n/* Loading shimmer animation - Original effect */\n.loading-shimmer-text {\n  font-weight: 600;\n  background-image: linear-gradient(\n    -75deg,\n    rgba(178, 178, 178, 0.8) 30%,\n    rgba(240, 240, 240, 0.95) 50%,\n    rgba(178, 178, 178, 0.8) 70%\n  );\n  background-size: 200% auto;\n  color: transparent;\n  background-clip: text;\n  -webkit-background-clip: text;\n  animation: textShimmer 1.8s linear infinite;\n}\n\n@keyframes textShimmer {\n  to {\n    background-position: -200% center;\n  }\n}\n\n/* LLM processing thoughts indicator */\n.llm-processing-thoughts {\n  font-style: italic;\n  color: var(--text-muted);\n  opacity: 0.8;\n}\n\n/* Chat message animations */\n.chat-message {\n  opacity: 0;\n  transform: translateY(15px);\n  animation: fadeIn 0.4s ease-out forwards;\n}\n\n@keyframes fadeIn {\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* SQL code container animations */\n.sql-code-container {\n  max-height: 0;\n  overflow: hidden;\n  opacity: 0;\n  transition: max-height 0.4s ease-in-out, opacity 0.4s ease-in-out, margin-top 0.3s ease-in-out, padding 0.3s ease-in-out;\n}\n\n.sql-code-container.expanded {\n  max-height: 500px;\n  opacity: 1;\n  margin-top: 8px;\n  padding: 10px;\n}\n\n/* Chart containers */\n.interactive-chart {\n  border-radius: 12px;\n  overflow: hidden !important;\n  background-color: var(--bg-primary);\n  transition: all 0.3s ease;\n}\n\n/* Responsive utilities */\n@media (max-width: 768px) {\n  .mobile-hidden {\n    display: none;\n  }\n}\n"], "names": [], "sourceRoot": ""}