import traceback
import pandas as pd
import sqlparse
import logging
from typing import Optional
from fastapi import APIRouter, Request, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from app.utils.dependencies import get_vanna, get_cache
from config import Config

router = APIRouter()
config = Config()
logger = logging.getLogger(__name__)

# Pydantic model for request body
class RunSqlRequest(BaseModel):
    id: str
    sql: Optional[str] = None

@router.get("/generate_sql")
async def generate_sql_api(
    question: str,
    existing_id: Optional[str] = None,
    allow_llm_to_see_data: bool = True,
    vn = Depends(get_vanna),
    cache = Depends(get_cache)
):
    """Generates SQL from a natural language question and streams the response with intermediate SQL support"""
    if not question:
        raise HTTPException(status_code=400, detail={"type": "error", "error": "No question provided"})

    # Generate ID or use existing one
    current_id = existing_id or cache.generate_id(question=question)

    # Cache the question
    cache.set(id=current_id, field='question', value=question)

    # Retrieve or initialize chat history for Vanna instance
    vn.chat_history = cache.get(id=current_id, field='chat_history') or []

    # Store the potentially updated chat history back into the cache
    cache.set(id=current_id, field='chat_history', value=vn.chat_history.copy())

    try:
        # Generate interactive response stream with intermediate SQL support
        response_stream = vn.generate_interactive_response(question, current_id, allow_llm_to_see_data=allow_llm_to_see_data)
        return StreamingResponse(response_stream, media_type='text/event-stream')
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail={"type": "error", "error": str(e), "id": current_id}
        )

@router.get("/run_sql")
async def run_sql_api(
    id: str,
    vn = Depends(get_vanna),
    cache = Depends(get_cache)
):
    """Execute SQL query and return results"""
    # Retrieve SQL from cache
    sql = cache.get(id=id, field='sql')
    if sql is None:
        raise HTTPException(
            status_code=400,
            detail={"type": "error", "error": "No SQL found in cache for this ID", "id": id}
        )

    if not vn.is_sql_valid(sql):
        raise HTTPException(
            status_code=400,
            detail={"type": "error", "error": "Invalid SQL. Only SELECT queries are allowed.", "sql": sql}
        )

    sql_to_attempt = sql
    if 'LIMIT' not in sql_to_attempt.upper():
        parsed = sqlparse.parse(sql_to_attempt)
        if parsed and parsed[0].get_type() == 'SELECT':
            sql_to_attempt = f"{sql_to_attempt} LIMIT {config.MAX_SQL_ROWS}"

    try:
        # First attempt
        df = vn.run_sql(sql=sql_to_attempt)

        # Handle large DataFrames
        df_size = df.memory_usage(deep=True).sum()
        sampled_message = ""
        if df_size > config.MAX_DF_SIZE_MB * 1024 * 1024:
            sample_fraction = min(1.0, (config.SAMPLE_DF_SIZE_MB * 1024 * 1024) / df_size)
            df_display = df.sample(frac=sample_fraction)
            sampled_message = "Note: Result set was too large and has been sampled for display and caching."
        else:
            df_display = df

        cache.set(id=id, field='df', value=df_display)

        # Add dataframe results to model context
        vn.add_dataframe_to_chat_history(sql=sql, df=df, cache_id=id, is_intermediate=False)

        df_json = df_display.to_json(orient='records') if not df_display.empty else "[]"

        metadata = {
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "column_names": df.columns.tolist() if not df.empty else [],
            "sampled_for_display": sampled_message != ""
        }

        # Create user-friendly message based on results
        if len(df) == 0:
            user_message = "Execution completed successfully. We couldn't find any information matching your request."
        else:
            user_message = f"SQL executed successfully. Retrieved {len(df)} rows.{' ' + sampled_message if sampled_message else ''}"

        return {
            "type": "df",
            "id": id,
            "sql_executed": sql,
            "df": df_json,
            "metadata": metadata,
            "message": user_message
        }
        
    except Exception as e_run_sql:
        # Handle SQL execution errors with correction attempts
        original_question = cache.get(id=id, field='question')
        if not original_question:
            raise HTTPException(
                status_code=500,
                detail={
                    "type": "error",
                    "error": f"Initial SQL execution failed: {str(e_run_sql)}. Correction not attempted (no question context).",
                    "failed_sql": sql
                }
            )

        try:
            corrected_sql_or_explanation = vn.generate_corrected_sql(
                question=original_question,
                sql=sql,
                error_msg=str(e_run_sql)
            )

            if corrected_sql_or_explanation.strip().startswith("ErrorExplanation:"):
                explanation = corrected_sql_or_explanation.replace("ErrorExplanation:", "").strip()
                raise HTTPException(
                    status_code=500,
                    detail={
                        "type": "error",
                        "id": id,
                        "error": f"Initial SQL execution failed: {str(e_run_sql)}.",
                        "llm_explanation": explanation,
                        "failed_sql": sql,
                    }
                )
            
            corrected_sql = corrected_sql_or_explanation

            if corrected_sql and corrected_sql.lower().strip() != sql.lower().strip() and vn.is_sql_valid(corrected_sql):
                sql_to_attempt_corrected = corrected_sql
                if 'LIMIT' not in sql_to_attempt_corrected.upper():
                    parsed_corrected = sqlparse.parse(sql_to_attempt_corrected)
                    if parsed_corrected and parsed_corrected[0].get_type() == 'SELECT':
                        sql_to_attempt_corrected = f"{sql_to_attempt_corrected} LIMIT {config.MAX_SQL_ROWS}"

                try:
                    df_corrected = vn.run_sql(sql=sql_to_attempt_corrected)
                    
                    # Handle large corrected DataFrames
                    df_corr_size = df_corrected.memory_usage(deep=True).sum()
                    sampled_message_corr = ""
                    if df_corr_size > config.MAX_DF_SIZE_MB * 1024 * 1024:
                        sample_fraction_corr = min(1.0, (config.SAMPLE_DF_SIZE_MB * 1024 * 1024) / df_corr_size)
                        df_corrected_display = df_corrected.sample(frac=sample_fraction_corr)
                        sampled_message_corr = "Note: Result set from corrected SQL was too large and has been sampled for display and caching."
                    else:
                        df_corrected_display = df_corrected

                    cache.set(id=id, field='sql', value=corrected_sql) 
                    cache.set(id=id, field='df', value=df_corrected_display)

                    # Add corrected dataframe results to model context
                    vn.add_dataframe_to_chat_history(sql=corrected_sql, df=df_corrected, cache_id=id, is_intermediate=False)

                    df_corr_json = df_corrected_display.to_json(orient='records') if not df_corrected_display.empty else "[]"

                    metadata_corr = {
                        "total_rows": len(df_corrected),
                        "total_columns": len(df_corrected.columns),
                        "column_names": df_corrected.columns.tolist() if not df_corrected.empty else [],
                        "sampled_for_display": sampled_message_corr != ""
                    }

                    # Create user-friendly message for corrected SQL based on results
                    if len(df_corrected) == 0:
                        corrected_user_message = "Original SQL failed. Corrected SQL executed successfully. We couldn't find any information matching your request."
                    else:
                        corrected_user_message = f"Original SQL failed. Corrected SQL executed successfully. Retrieved {len(df_corrected)} rows.{' ' + sampled_message_corr if sampled_message_corr else ''}"

                    return {
                        "type": "df",
                        "id": id,
                        "original_sql": sql,
                        "corrected_sql_executed": corrected_sql,
                        "df": df_corr_json,
                        "metadata": metadata_corr,
                        "message": corrected_user_message
                    }

                except Exception as e_corrected_run_sql:
                    raise HTTPException(
                        status_code=500,
                        detail={
                            "type": "error",
                            "error": f"Initial SQL failed: {str(e_run_sql)}. Corrected SQL also failed: {str(e_corrected_run_sql)}",
                            "failed_sql": sql,
                            "attempted_corrected_sql": corrected_sql
                        }
                    )
            else:
                raise HTTPException(
                    status_code=500,
                    detail={
                        "type": "error",
                        "error": f"Initial SQL execution failed: {str(e_run_sql)}. LLM correction was not valid or different.",
                        "failed_sql": sql,
                        "llm_correction_output": corrected_sql if corrected_sql else None
                    }
                )

        except Exception as e_correction_generation:
            raise HTTPException(
                status_code=500,
                detail={
                    "type": "error",
                    "error": f"Initial SQL execution failed: {str(e_run_sql)}. Error during correction attempt: {str(e_correction_generation)}",
                    "failed_sql": sql
                }
            )
